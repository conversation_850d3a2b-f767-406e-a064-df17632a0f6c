import mongoose from 'mongoose';
const { Schema } = mongoose;

const PageSchema = new Schema({
  // Page section identifier - corresponds to navbar links
  section: {
    type: String,
    required: true,
    enum: ['the island', 'experiences', 'testimonials', 'location & contacts'],
    unique: true
  },
  
  // Content text for the page section
  text: {
    type: String,
    required: true,
    maxlength: [5000, 'Text content cannot exceed 5000 characters']
  },
  
  // Firebase Storage URL for the section image
  image: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        // Validate Firebase Storage URL format
        return /^https:\/\/firebasestorage\.googleapis\.com/.test(v);
      },
      message: 'Image must be a valid Firebase Storage URL'
    }
  },
  
  // External link URL (only for 'location & contacts' section)
  url: {
    type: String,
    default: '',
    validate: {
      validator: function(v) {
        // Only validate URL format if it's provided and this is location & contacts section
        if (!v || this.section !== 'location & contacts') return true;
        return /^https?:\/\/.+/.test(v);
      },
      message: 'URL must be a valid HTTP/HTTPS URL'
    }
  },
  
  // Additional metadata
  fullPath: {
    type: String,
    default: ''
  },
  
  contentType: {
    type: String,
    default: 'image/jpeg'
  },
  
  size: {
    type: Number,
    default: 0
  },
  
  uploadedAt: {
    type: String,
    default: ''
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
PageSchema.index({ section: 1 });
PageSchema.index({ createdAt: -1 });

// Virtual for display name
PageSchema.virtual('displayName').get(function() {
  return this.section.split(' ').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
});

// Pre-save middleware to ensure proper data structure
PageSchema.pre('save', function(next) {
  // Ensure URL is only set for location & contacts section
  if (this.section !== 'location & contacts') {
    this.url = '';
  }
  
  // Set upload timestamp if not provided
  if (!this.uploadedAt) {
    this.uploadedAt = new Date().toISOString();
  }
  
  next();
});

// Static method to initialize default pages
PageSchema.statics.initializeDefaultPages = async function() {
  const sections = ['the island', 'experiences', 'testimonials', 'location & contacts'];
  
  for (const section of sections) {
    const existingPage = await this.findOne({ section });
    
    if (!existingPage) {
      await this.create({
        section,
        text: `Welcome to the ${section} section. This content can be updated through the admin panel.`,
        image: 'https://firebasestorage.googleapis.com/placeholder.jpg', // Placeholder URL
        url: section === 'location & contacts' ? 'https://example.com' : ''
      });
    }
  }
};

export const Page = mongoose.models.Page || mongoose.model('Page', PageSchema);
