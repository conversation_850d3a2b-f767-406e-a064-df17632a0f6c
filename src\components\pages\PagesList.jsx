'use client';

import { useState, useCallback, useMemo } from 'react';
import { MdEdit, MdDelete, MdImage, MdLink, MdCheckBox, MdCheckBoxOutlineBlank } from 'react-icons/md';

export default function PagesList({
  pages = [],
  onEdit,
  onDelete,
  isLoading = false
}) {
  const [selectedPages, setSelectedPages] = useState(new Set());
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleSelectAll = useCallback(() => {
    if (selectedPages.size === pages.length) {
      setSelectedPages(new Set());
    } else {
      setSelectedPages(new Set(pages.map(page => page._id)));
    }
  }, [pages, selectedPages.size]);

  const handleSelectPage = useCallback((pageId) => {
    setSelectedPages(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(pageId)) {
        newSelected.delete(pageId);
      } else {
        newSelected.add(pageId);
      }
      return newSelected;
    });
  }, []);

  const handleBulkDelete = useCallback(() => {
    if (selectedPages.size > 0) {
      setShowDeleteConfirm(true);
    }
  }, [selectedPages.size]);

  const confirmBulkDelete = useCallback(() => {
    onDelete(Array.from(selectedPages));
    setSelectedPages(new Set());
    setShowDeleteConfirm(false);
  }, [selectedPages, onDelete]);

  const formatDate = useCallback((dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }, []);

  const truncateText = useCallback((text, maxLength = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }, []);

  const memoizedPages = useMemo(() => pages, [pages]);

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (memoizedPages.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <MdImage className="mx-auto text-6xl text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Pages Found</h3>
          <p className="text-gray-500">
            Get started by creating page content for your navbar sections.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            Pages ({memoizedPages.length})
          </h3>
          
          {selectedPages.size > 0 && (
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-500">
                {selectedPages.size} selected
              </span>
              <button
                onClick={handleBulkDelete}
                className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                Delete Selected
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <button
                  onClick={handleSelectAll}
                  className="text-gray-400 hover:text-gray-600"
                >
                  {selectedPages.size === memoizedPages.length ? (
                    <MdCheckBox className="text-blue-600" />
                  ) : (
                    <MdCheckBoxOutlineBlank />
                  )}
                </button>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Section
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Content Preview
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Image
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                URL
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Updated
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {memoizedPages.map((page) => (
              <tr key={page._id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <button
                    onClick={() => handleSelectPage(page._id)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {selectedPages.has(page._id) ? (
                      <MdCheckBox className="text-blue-600" />
                    ) : (
                      <MdCheckBoxOutlineBlank />
                    )}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 capitalize">
                    {page.section}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900 max-w-xs">
                    {truncateText(page.text)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {page.image && (
                    <img
                      src={page.image}
                      alt={`${page.section} preview`}
                      className="h-12 w-12 object-cover rounded-lg border border-gray-300"
                    />
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {page.url && (
                    <a
                      href={page.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 flex items-center"
                    >
                      <MdLink className="mr-1" />
                      <span className="text-sm">Link</span>
                    </a>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(page.updatedAt)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => onEdit(page)}
                      className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                      title="Edit page"
                    >
                      <MdEdit />
                    </button>
                    <button
                      onClick={() => onDelete([page._id])}
                      className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                      title="Delete page"
                    >
                      <MdDelete />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Confirm Deletion
            </h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete {selectedPages.size} page(s)? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={confirmBulkDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
