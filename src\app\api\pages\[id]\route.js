import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Page } from '@/models/Page';

// GET /api/pages/[id] - Get single page (no authentication required)
export async function GET(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;

    // Support lookup by MongoDB ID or section name
    let page;
    
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // MongoDB ObjectId format
      page = await Page.findById(id);
    } else {
      // Section name lookup
      page = await Page.findOne({ section: id });
    }

    if (!page) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Page not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: page,
      message: 'Page retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching page:', error);

    if (error.name === 'CastError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid ID',
          message: 'Invalid page ID format',
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch page',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/pages/[id] - Update single page (no authentication required)
export async function PUT(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;
    const body = await request.json();

    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;

    const updatedPage = await Page.findByIdAndUpdate(
      id,
      body,
      { new: true, runValidators: true }
    );

    if (!updatedPage) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Page not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedPage,
      message: 'Page updated successfully',
    });
  } catch (error) {
    console.error('Error updating page:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    if (error.name === 'CastError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid ID',
          message: 'Invalid page ID format',
        },
        { status: 400 }
      );
    }

    if (error.code === 11000) {
      return NextResponse.json(
        {
          success: false,
          error: 'Duplicate Error',
          message: 'A page with this section already exists',
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update page',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/pages/[id] - Partial update single page (no authentication required)
export async function PATCH(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;
    const body = await request.json();

    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;

    const updatedPage = await Page.findByIdAndUpdate(
      id,
      { $set: body },
      { new: true, runValidators: true }
    );

    if (!updatedPage) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Page not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedPage,
      message: 'Page updated successfully',
    });
  } catch (error) {
    console.error('Error updating page:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    if (error.name === 'CastError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid ID',
          message: 'Invalid page ID format',
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update page',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/pages/[id] - Delete single page (no authentication required)
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;

    const deletedPage = await Page.findByIdAndDelete(id);

    if (!deletedPage) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Page not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: deletedPage,
      message: 'Page deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting page:', error);

    if (error.name === 'CastError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid ID',
          message: 'Invalid page ID format',
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete page',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
