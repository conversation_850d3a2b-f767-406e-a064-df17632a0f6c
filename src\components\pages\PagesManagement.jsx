'use client';

import { useState, useEffect, useCallback } from 'react';
import { MdAdd, MdSearch, MdRefresh, MdArrowBack } from 'react-icons/md';
import PagesForm from './PagesForm';
import PagesList from './PagesList';

export default function PagesManagement() {
  const [pages, setPages] = useState([]);
  const [filteredPages, setFilteredPages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingPage, setEditingPage] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Fetch pages
  const fetchPages = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/pages');
      const result = await response.json();

      if (result.success) {
        setPages(result.data);
        setError('');
      } else {
        setError(result.message || 'Failed to fetch pages');
      }
    } catch (error) {
      console.error('Error fetching pages:', error);
      setError('Failed to fetch pages');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Filter pages based on search
  useEffect(() => {
    let filtered = pages;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(page =>
        page.section.toLowerCase().includes(searchTerm.toLowerCase()) ||
        page.text.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredPages(filtered);
  }, [pages, searchTerm]);

  // Load pages on component mount
  useEffect(() => {
    fetchPages();
  }, [fetchPages]);

  // Auto-clear messages
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(''), 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(''), 8000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  const handleAdd = useCallback(() => {
    setEditingPage(null);
    setShowForm(true);
  }, []);

  const handleEdit = useCallback((page) => {
    setEditingPage(page);
    setShowForm(true);
  }, []);

  const handleSave = useCallback(async (formData) => {
    try {
      setIsSaving(true);
      setError('');

      // Always use POST for pages (handles both create and update)
      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        const count = Array.isArray(formData) ? formData.length : 1;
        setSuccess(`${count} page(s) saved successfully`);
        setShowForm(false);
        setEditingPage(null);
        await fetchPages();
      } else {
        setError(result.message || 'Failed to save pages');
      }
    } catch (error) {
      console.error('Error saving pages:', error);
      setError('Failed to save pages');
    } finally {
      setIsSaving(false);
    }
  }, [fetchPages]);

  const handleDelete = useCallback(async (pageIds) => {
    try {
      setIsLoading(true);
      setError('');

      const response = await fetch(`/api/pages?ids=${pageIds.join(',')}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        setSuccess(`${result.data.deletedCount} page(s) deleted successfully`);
        await fetchPages();
      } else {
        setError(result.message || 'Failed to delete pages');
      }
    } catch (error) {
      console.error('Error deleting pages:', error);
      setError('Failed to delete pages');
    } finally {
      setIsLoading(false);
    }
  }, [fetchPages]);

  const handleCancel = useCallback(() => {
    setShowForm(false);
    setEditingPage(null);
  }, []);

  const handleRefresh = useCallback(() => {
    fetchPages();
  }, [fetchPages]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Pages Management</h1>
          <p className="mt-2 text-gray-600">
            Manage content for navbar sections: The Island, Experiences, Testimonials, and Location & Contacts
          </p>
        </div>
        
        {!showForm && (
          <div className="flex space-x-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <MdRefresh className="inline mr-2" />
              Refresh
            </button>
            <button
              onClick={handleAdd}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <MdAdd className="inline mr-2" />
              Manage Pages
            </button>
          </div>
        )}

        {showForm && (
          <button
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <MdArrowBack className="inline mr-2" />
            Back to List
          </button>
        )}
      </div>

      {/* Messages */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {success}
        </div>
      )}

      {/* Form */}
      {showForm && (
        <PagesForm
          pages={pages}
          onSave={handleSave}
          onCancel={handleCancel}
          isLoading={isSaving}
        />
      )}

      {/* Filters */}
      {!showForm && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            {/* Search */}
            <div className="relative">
              <MdSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search pages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Stats */}
            <div className="text-sm text-gray-600">
              Showing {filteredPages.length} of {pages.length} pages
            </div>
          </div>
        </div>
      )}

      {/* List */}
      {!showForm && (
        <PagesList
          pages={filteredPages}
          onEdit={handleEdit}
          onDelete={handleDelete}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
